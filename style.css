
:root {
  /* Colors */
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);
  --color-select-caret: rgba(19, 52, 59, 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: rgba(31, 33, 33, 1);
    --color-surface: rgba(38, 40, 40, 1);
    --color-text: rgba(245, 245, 245, 1);
    --color-text-secondary: rgba(167, 169, 169, 0.7);
    --color-primary: rgba(50, 184, 198, 1);
    --color-primary-hover: rgba(45, 166, 178, 1);
    --color-primary-active: rgba(41, 150, 161, 1);
    --color-secondary: rgba(119, 124, 124, 0.15);
    --color-secondary-hover: rgba(119, 124, 124, 0.25);
    --color-secondary-active: rgba(119, 124, 124, 0.3);
    --color-border: rgba(119, 124, 124, 0.3);
    --color-error: rgba(255, 84, 89, 1);
    --color-success: rgba(50, 184, 198, 1);
    --color-warning: rgba(230, 129, 97, 1);
    --color-info: rgba(167, 169, 169, 1);
    --color-focus-ring: rgba(50, 184, 198, 0.4);
    --color-btn-primary-text: rgba(19, 52, 59, 1);
    --color-card-border: rgba(119, 124, 124, 0.2);
    --color-card-border-inner: rgba(119, 124, 124, 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(119, 124, 124, 0.2);
    --color-border-secondary: rgba(119, 124, 124, 0.2);
    --color-select-caret: rgba(245, 245, 245, 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: 50, 184, 198;
    --color-error-rgb: 255, 84, 89;
    --color-warning-rgb: 230, 129, 97;
    --color-info-rgb: 167, 169, 169;
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  --color-background: rgba(31, 33, 33, 1);
  --color-surface: rgba(38, 40, 40, 1);
  --color-text: rgba(245, 245, 245, 1);
  --color-text-secondary: rgba(167, 169, 169, 0.7);
  --color-primary: rgba(50, 184, 198, 1);
  --color-primary-hover: rgba(45, 166, 178, 1);
  --color-primary-active: rgba(41, 150, 161, 1);
  --color-secondary: rgba(119, 124, 124, 0.15);
  --color-secondary-hover: rgba(119, 124, 124, 0.25);
  --color-secondary-active: rgba(119, 124, 124, 0.3);
  --color-border: rgba(119, 124, 124, 0.3);
  --color-error: rgba(255, 84, 89, 1);
  --color-success: rgba(50, 184, 198, 1);
  --color-warning: rgba(230, 129, 97, 1);
  --color-info: rgba(167, 169, 169, 1);
  --color-focus-ring: rgba(50, 184, 198, 0.4);
  --color-btn-primary-text: rgba(19, 52, 59, 1);
  --color-card-border: rgba(119, 124, 124, 0.15);
  --color-card-border-inner: rgba(119, 124, 124, 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(119, 124, 124, 0.2);
  --color-select-caret: rgba(245, 245, 245, 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: 50, 184, 198;
  --color-error-rgb: 255, 84, 89;
  --color-warning-rgb: 230, 129, 97;
  --color-info-rgb: 167, 169, 169;
}

[data-color-scheme="light"] {
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* Header and Navigation */
.header {
  background: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow-sm);
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-16) 0;
}

.nav__brand h1 {
  color: var(--color-primary);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
}

.nav__menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-24);
}

.nav__link {
  color: var(--color-text);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--duration-fast) var(--ease-standard);
}

.nav__link:hover {
  color: var(--color-primary);
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--color-surface) 0%, var(--color-secondary) 100%);
  padding: var(--space-32) 0;
  text-align: center;
}

.hero__content {
  margin-bottom: var(--space-32);
}

.hero__content h1 {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--space-16);
  color: var(--color-text);
}

.hero__content p {
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Platform Cards */
.platform-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-24);
  margin-top: var(--space-32);
}

.platform-card {
  background: var(--color-surface);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  box-shadow: var(--shadow-sm);
}

.platform-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.platform-card__header {
  margin-bottom: var(--space-16);
}

.platform-card__header h3 {
  color: var(--color-text);
  margin-bottom: var(--space-4);
}

.platform-card__subtitle {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.platform-card__content p {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-16);
}

.platform-card__highlights {
  display: flex;
  gap: var(--space-8);
  flex-wrap: wrap;
}

/* Platforms Section */
.platforms {
  padding: var(--space-32) 0;
  background: var(--color-background);
}

.platforms h2 {
  text-align: center;
  margin-bottom: var(--space-32);
  color: var(--color-text);
}

.platform-tabs {
  max-width: 1000px;
  margin: 0 auto;
}

.tab-buttons {
  display: flex;
  gap: var(--space-4);
  margin-bottom: var(--space-24);
  background: var(--color-secondary);
  border-radius: var(--radius-md);
  padding: var(--space-4);
}

.tab-button {
  flex: 1;
  padding: var(--space-12) var(--space-16);
  border: none;
  border-radius: var(--radius-base);
  background: transparent;
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
}

.tab-button.active {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.tab-button:hover:not(.active) {
  background: var(--color-secondary-hover);
}

.tab-content {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  overflow: hidden;
}

.tab-pane {
  display: none;
  padding: var(--space-32);
}

.tab-pane.active {
  display: block;
}

.platform-detail h3 {
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.platform-detail > p {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-24);
}

.platform-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-24);
}

.platform-section {
  background: var(--color-background);
  padding: var(--space-20);
  border-radius: var(--radius-base);
  border: 1px solid var(--color-card-border-inner);
}

.platform-section h4 {
  color: var(--color-primary);
  margin-bottom: var(--space-12);
}

.platform-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.platform-section li {
  color: var(--color-text);
  margin-bottom: var(--space-8);
  padding-left: var(--space-16);
  position: relative;
}

.platform-section li::before {
  content: "•";
  color: var(--color-primary);
  position: absolute;
  left: 0;
}

/* Comparison Section */
.comparison {
  padding: var(--space-32) 0;
  background: var(--color-surface);
}

.comparison h2 {
  text-align: center;
  margin-bottom: var(--space-32);
  color: var(--color-text);
}

.comparison-controls {
  margin-bottom: var(--space-24);
  text-align: center;
}

.category-toggles {
  display: flex;
  gap: var(--space-8);
  justify-content: center;
  flex-wrap: wrap;
}

.toggle-btn {
  padding: var(--space-8) var(--space-16);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  background: var(--color-background);
  color: var(--color-text);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
}

.toggle-btn.active {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  border-color: var(--color-primary);
}

.toggle-btn:hover:not(.active) {
  background: var(--color-secondary);
}

.comparison-table {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-card-border);
}

.comparison-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  background: var(--color-secondary);
  font-weight: var(--font-weight-semibold);
}

.comparison-cell {
  padding: var(--space-16);
  border-right: 1px solid var(--color-card-border-inner);
  color: var(--color-text);
}

.comparison-cell:last-child {
  border-right: none;
}

.comparison-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  border-bottom: 1px solid var(--color-card-border-inner);
}

.comparison-row:last-child {
  border-bottom: none;
}

.comparison-row:nth-child(even) {
  background: var(--color-background);
}

.comparison-category {
  background: var(--color-secondary);
  grid-column: 1 / -1;
  padding: var(--space-12) var(--space-16);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Decision Section */
.decision {
  padding: var(--space-32) 0;
  background: var(--color-background);
}

.decision h2 {
  text-align: center;
  margin-bottom: var(--space-32);
  color: var(--color-text);
}

.decision-tool {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
  max-width: 1200px;
  margin: 0 auto;
}

.decision-questions {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  border: 1px solid var(--color-card-border);
}

.question {
  display: none;
  margin-bottom: var(--space-24);
}

.question.active {
  display: block;
}

.question h3 {
  color: var(--color-text);
  margin-bottom: var(--space-16);
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.option-btn {
  padding: var(--space-12) var(--space-16);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  background: var(--color-background);
  color: var(--color-text);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  text-align: left;
}

.option-btn:hover {
  background: var(--color-secondary);
}

.option-btn.selected {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  border-color: var(--color-primary);
}

.decision-result {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  border: 1px solid var(--color-card-border);
  text-align: center;
}

.decision-result h3 {
  color: var(--color-text);
  margin-bottom: var(--space-16);
}

.decision-result p {
  color: var(--color-text-secondary);
}

.recommendation {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  padding: var(--space-16);
  border-radius: var(--radius-base);
  margin-top: var(--space-16);
}

.recommendation h4 {
  margin-bottom: var(--space-8);
}

/* Migration Section */
.migration {
  padding: var(--space-32) 0;
  background: var(--color-surface);
}

.migration h2 {
  text-align: center;
  margin-bottom: var(--space-32);
  color: var(--color-text);
}

.migration-paths {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-24);
}

.migration-card {
  background: var(--color-background);
  border: 1px solid var(--color-card-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  box-shadow: var(--shadow-sm);
}

.migration-card h3 {
  color: var(--color-text);
  margin-bottom: var(--space-16);
}

.migration-complexity {
  margin-bottom: var(--space-16);
}

.complexity-badge {
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.complexity-badge.low {
  background: rgba(var(--color-success-rgb), 0.15);
  color: var(--color-success);
}

.complexity-badge.medium {
  background: rgba(var(--color-warning-rgb), 0.15);
  color: var(--color-warning);
}

.complexity-badge.high {
  background: rgba(var(--color-error-rgb), 0.15);
  color: var(--color-error);
}

.migration-tools,
.migration-considerations {
  margin-bottom: var(--space-16);
}

.migration-tools h4,
.migration-considerations h4 {
  color: var(--color-primary);
  margin-bottom: var(--space-8);
  font-size: var(--font-size-base);
}

.migration-tools ul,
.migration-considerations ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.migration-tools li,
.migration-considerations li {
  color: var(--color-text);
  margin-bottom: var(--space-4);
  padding-left: var(--space-16);
  position: relative;
}

.migration-tools li::before,
.migration-considerations li::before {
  content: "•";
  color: var(--color-primary);
  position: absolute;
  left: 0;
}

/* Cost Calculator */
.cost-calculator {
  padding: var(--space-32) 0;
  background: var(--color-background);
}

.cost-calculator h2 {
  text-align: center;
  margin-bottom: var(--space-32);
  color: var(--color-text);
}

.calculator {
  max-width: 1000px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--space-32);
}

.calculator-inputs {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  border: 1px solid var(--color-card-border);
}

.calculator-results {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  border: 1px solid var(--color-card-border);
}

.calculator-results h3 {
  color: var(--color-text);
  margin-bottom: var(--space-20);
  text-align: center;
}

.cost-breakdown {
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
}

.cost-item {
  background: var(--color-background);
  padding: var(--space-16);
  border-radius: var(--radius-base);
  border: 1px solid var(--color-card-border-inner);
}

.cost-item h4 {
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.cost-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-8);
}

.cost-details p {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-4);
}

/* Footer */
.footer {
  background: var(--color-surface);
  border-top: 1px solid var(--color-border);
  padding: var(--space-24) 0;
  text-align: center;
}

.footer p {
  color: var(--color-text-secondary);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav {
    flex-direction: column;
    gap: var(--space-16);
  }

  .nav__menu {
    gap: var(--space-16);
    flex-wrap: wrap;
    justify-content: center;
  }

  .hero__content h1 {
    font-size: var(--font-size-3xl);
  }

  .platform-cards {
    grid-template-columns: 1fr;
  }

  .platform-sections {
    grid-template-columns: 1fr;
  }

  .tab-buttons {
    flex-direction: column;
  }

  .comparison-header,
  .comparison-row {
    grid-template-columns: 1fr;
  }

  .comparison-cell {
    border-right: none;
    border-bottom: 1px solid var(--color-card-border-inner);
  }

  .decision-tool {
    grid-template-columns: 1fr;
  }

  .calculator {
    grid-template-columns: 1fr;
  }

  .migration-paths {
    grid-template-columns: 1fr;
  }

  .category-toggles {
    flex-direction: column;
    align-items: center;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Loading states */
.loading {
  opacity: 0.5;
  pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Progress indicator for decision tool */
.progress-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: var(--space-16);
}

.progress-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--color-border);
  margin: 0 var(--space-4);
  transition: background-color var(--duration-fast) var(--ease-standard);
}

.progress-dot.active {
  background: var(--color-primary);
}

.progress-dot.completed {
  background: var(--color-success);
}

/* Highlight animations */
.highlight {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-sm);
  animation: highlight 0.5s ease-out;
}

@keyframes highlight {
  0% {
    background: var(--color-warning);
  }
  100% {
    background: var(--color-primary);
  }
}