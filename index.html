<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VM Platform Comparison Guide</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="nav__brand">
                    <h1>VM Platform Guide</h1>
                </div>
                <ul class="nav__menu">
                    <li><a href="#overview" class="nav__link">Overview</a></li>
                    <li><a href="#platforms" class="nav__link">Platforms</a></li>
                    <li><a href="#comparison" class="nav__link">Compare</a></li>
                    <li><a href="#decision" class="nav__link">Decision Tool</a></li>
                    <li><a href="#migration" class="nav__link">Migration</a></li>
                    <li><a href="#cost" class="nav__link">Cost Calculator</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <!-- Overview Section -->
        <section id="overview" class="hero">
            <div class="container">
                <div class="hero__content">
                    <h1>Virtual Machine Platform Comparison</h1>
                    <p>Compare Rancher Harvester, OpenShift Virtualization, and VMware vSphere for your VM workloads</p>
                </div>
                <div class="platform-cards">
                    <div class="platform-card" data-platform="harvester">
                        <div class="platform-card__header">
                            <h3>Rancher Harvester</h3>
                            <span class="platform-card__subtitle">Open-source HCI</span>
                        </div>
                        <div class="platform-card__content">
                            <p>Modern, open-source hyper-converged infrastructure built on Kubernetes</p>
                            <div class="platform-card__highlights">
                                <span class="status status--success">Free & Open Source</span>
                                <span class="status status--info">Kubernetes Native</span>
                            </div>
                        </div>
                    </div>

                    <div class="platform-card" data-platform="openshift">
                        <div class="platform-card__header">
                            <h3>OpenShift Virtualization</h3>
                            <span class="platform-card__subtitle">Hybrid cloud platform</span>
                        </div>
                        <div class="platform-card__content">
                            <p>Kubernetes-native virtualization enabling unified VM and container management</p>
                            <div class="platform-card__highlights">
                                <span class="status status--warning">Subscription Based</span>
                                <span class="status status--info">VM + Container</span>
                            </div>
                        </div>
                    </div>

                    <div class="platform-card" data-platform="vmware">
                        <div class="platform-card__header">
                            <h3>VMware vSphere</h3>
                            <span class="platform-card__subtitle">Enterprise virtualization leader</span>
                        </div>
                        <div class="platform-card__content">
                            <p>Industry-standard enterprise virtualization platform with mature ecosystem</p>
                            <div class="platform-card__highlights">
                                <span class="status status--error">Premium License</span>
                                <span class="status status--success">Best Performance</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Platform Deep Dive Section -->
        <section id="platforms" class="platforms">
            <div class="container">
                <h2>Platform Deep Dive</h2>
                <div class="platform-tabs">
                    <div class="tab-buttons">
                        <button class="tab-button active" data-tab="harvester">Rancher Harvester</button>
                        <button class="tab-button" data-tab="openshift">OpenShift Virtualization</button>
                        <button class="tab-button" data-tab="vmware">VMware vSphere</button>
                    </div>
                    <div class="tab-content">
                        <div class="tab-pane active" id="tab-harvester">
                            <div class="platform-detail">
                                <h3>Rancher Harvester</h3>
                                <p>Modern, open-source hyper-converged infrastructure built on Kubernetes</p>
                                <div class="platform-sections">
                                    <div class="platform-section">
                                        <h4>Architecture</h4>
                                        <ul>
                                            <li><strong>Core Technology:</strong> KubeVirt on Kubernetes</li>
                                            <li><strong>VM Management:</strong> VMs as native Kubernetes objects</li>
                                            <li><strong>Storage:</strong> Longhorn (distributed block storage)</li>
                                            <li><strong>Networking:</strong> VLAN, VIP, multi-NIC support</li>
                                        </ul>
                                    </div>
                                    <div class="platform-section">
                                        <h4>Key Features</h4>
                                        <ul>
                                            <li>Open-source (free licensing)</li>
                                            <li>Live migration with zero downtime</li>
                                            <li>High availability with witness nodes</li>
                                            <li>GPU support (vGPU)</li>
                                            <li>Backup to NFS, S3, NAS</li>
                                        </ul>
                                    </div>
                                    <div class="platform-section">
                                        <h4>Best For</h4>
                                        <ul>
                                            <li>Cost-sensitive organizations</li>
                                            <li>Kubernetes-native environments</li>
                                            <li>Modern infrastructure modernization</li>
                                            <li>Organizations avoiding vendor lock-in</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane" id="tab-openshift">
                            <div class="platform-detail">
                                <h3>OpenShift Virtualization</h3>
                                <p>Kubernetes-native virtualization enabling unified VM and container management</p>
                                <div class="platform-sections">
                                    <div class="platform-section">
                                        <h4>Architecture</h4>
                                        <ul>
                                            <li><strong>Core Technology:</strong> KubeVirt on Kubernetes</li>
                                            <li><strong>VM Management:</strong> VMs as pods via virtualization operator</li>
                                            <li><strong>Storage:</strong> CSI drivers, OpenShift Data Foundation</li>
                                            <li><strong>Networking:</strong> OpenShift SDN, OVN-Kubernetes</li>
                                        </ul>
                                    </div>
                                    <div class="platform-section">
                                        <h4>Key Features</h4>
                                        <ul>
                                            <li>Unified VM/container platform</li>
                                            <li>Enterprise-grade security</li>
                                            <li>Migration tools from VMware</li>
                                            <li>CI/CD integration</li>
                                            <li>Multi-cloud support</li>
                                        </ul>
                                    </div>
                                    <div class="platform-section">
                                        <h4>Best For</h4>
                                        <ul>
                                            <li>Hybrid VM/container environments</li>
                                            <li>Infrastructure modernization</li>
                                            <li>Organizations with Red Hat ecosystem</li>
                                            <li>DevOps-focused environments</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane" id="tab-vmware">
                            <div class="platform-detail">
                                <h3>VMware vSphere</h3>
                                <p>Industry-standard enterprise virtualization platform with mature ecosystem</p>
                                <div class="platform-sections">
                                    <div class="platform-section">
                                        <h4>Architecture</h4>
                                        <ul>
                                            <li><strong>Core Technology:</strong> ESXi hypervisor</li>
                                            <li><strong>VM Management:</strong> Dedicated VM management via vCenter</li>
                                            <li><strong>Storage:</strong> vSAN, external storage arrays</li>
                                            <li><strong>Networking:</strong> vSphere Standard Switch, NSX</li>
                                        </ul>
                                    </div>
                                    <div class="platform-section">
                                        <h4>Key Features</h4>
                                        <ul>
                                            <li>Best-in-class VM performance</li>
                                            <li>vMotion live migration</li>
                                            <li>vSphere HA and Fault Tolerance</li>
                                            <li>Extensive third-party ecosystem</li>
                                            <li>Advanced automation (DRS, HA)</li>
                                        </ul>
                                    </div>
                                    <div class="platform-section">
                                        <h4>Best For</h4>
                                        <ul>
                                            <li>Mission-critical enterprise workloads</li>
                                            <li>High-density VM environments</li>
                                            <li>Organizations with existing VMware investments</li>
                                            <li>Maximum performance requirements</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Side-by-Side Comparison -->
        <section id="comparison" class="comparison">
            <div class="container">
                <h2>Side-by-Side Comparison</h2>
                <div class="comparison-controls">
                    <div class="category-toggles">
                        <button class="toggle-btn active" data-category="architecture">Architecture</button>
                        <button class="toggle-btn active" data-category="features">Features</button>
                        <button class="toggle-btn active" data-category="performance">Performance</button>
                        <button class="toggle-btn active" data-category="cost">Cost</button>
                    </div>
                </div>
                <div class="comparison-table">
                    <div class="comparison-header">
                        <div class="comparison-cell">Feature</div>
                        <div class="comparison-cell">Rancher Harvester</div>
                        <div class="comparison-cell">OpenShift Virtualization</div>
                        <div class="comparison-cell">VMware vSphere</div>
                    </div>
                    <div class="comparison-body" id="comparison-body">
                        <!-- Dynamically populated -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Decision Matrix -->
        <section id="decision" class="decision">
            <div class="container">
                <h2>Decision Matrix Tool</h2>
                <div class="decision-tool">
                    <div class="decision-questions">
                        <div class="question active" data-question="workload-type">
                            <h3>What type of workloads will you primarily run?</h3>
                            <div class="question-options">
                                <button class="option-btn" data-value="pure-vm">Pure VM workloads</button>
                                <button class="option-btn" data-value="hybrid">Hybrid VM + Container workloads</button>
                                <button class="option-btn" data-value="container-focus">Container-focused with some VMs</button>
                            </div>
                        </div>
                        <div class="question" data-question="cost-priority">
                            <h3>How important is cost optimization?</h3>
                            <div class="question-options">
                                <button class="option-btn" data-value="cost-critical">Cost is critical</button>
                                <button class="option-btn" data-value="cost-important">Cost is important</button>
                                <button class="option-btn" data-value="cost-secondary">Performance over cost</button>
                            </div>
                        </div>
                        <div class="question" data-question="scale">
                            <h3>What is your expected scale?</h3>
                            <div class="question-options">
                                <button class="option-btn" data-value="small">Small (< 100 VMs)</button>
                                <button class="option-btn" data-value="medium">Medium (100-1000 VMs)</button>
                                <button class="option-btn" data-value="large">Large (1000+ VMs)</button>
                            </div>
                        </div>
                    </div>
                    <div class="decision-result" id="decision-result">
                        <h3>Get Your Recommendation</h3>
                        <p>Answer the questions above to get a personalized platform recommendation.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Migration Guide -->
        <section id="migration" class="migration">
            <div class="container">
                <h2>Migration Guide</h2>
                <div class="migration-paths">
                    <div class="migration-card">
                        <h3>VMware to Harvester</h3>
                        <div class="migration-complexity">
                            <span class="complexity-badge medium">Medium Complexity</span>
                        </div>
                        <div class="migration-tools">
                            <h4>Tools Required:</h4>
                            <ul>
                                <li>vm-import-controller</li>
                                <li>qemu-img conversion</li>
                                <li>Coriolis (optional)</li>
                            </ul>
                        </div>
                        <div class="migration-considerations">
                            <h4>Key Considerations:</h4>
                            <ul>
                                <li>Manual process required</li>
                                <li>Disk format conversion needed</li>
                                <li>Network mapping required</li>
                                <li>Test thoroughly before production</li>
                            </ul>
                        </div>
                    </div>
                    <div class="migration-card">
                        <h3>VMware to OpenShift</h3>
                        <div class="migration-complexity">
                            <span class="complexity-badge low">Low-Medium Complexity</span>
                        </div>
                        <div class="migration-tools">
                            <h4>Tools Required:</h4>
                            <ul>
                                <li>Migration Toolkit for Virtualization (MTV)</li>
                                <li>Red Hat migration tools</li>
                                <li>Crane (for containers)</li>
                            </ul>
                        </div>
                        <div class="migration-considerations">
                            <h4>Key Considerations:</h4>
                            <ul>
                                <li>Automated migration tools available</li>
                                <li>Good Red Hat support</li>
                                <li>Gradual migration possible</li>
                                <li>Network policies need review</li>
                            </ul>
                        </div>
                    </div>
                    <div class="migration-card">
                        <h3>Physical to Virtual</h3>
                        <div class="migration-complexity">
                            <span class="complexity-badge high">High Complexity</span>
                        </div>
                        <div class="migration-tools">
                            <h4>Tools Required:</h4>
                            <ul>
                                <li>Various P2V tools</li>
                                <li>Disk cloning solutions</li>
                                <li>Network configuration tools</li>
                            </ul>
                        </div>
                        <div class="migration-considerations">
                            <h4>Key Considerations:</h4>
                            <ul>
                                <li>Requires physical-to-virtual conversion</li>
                                <li>Hardware dependency issues</li>
                                <li>Driver compatibility concerns</li>
                                <li>Extended downtime possible</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Cost Calculator -->
        <section id="cost" class="cost-calculator">
            <div class="container">
                <h2>Cost Calculator</h2>
                <div class="calculator">
                    <div class="calculator-inputs">
                        <div class="form-group">
                            <label for="cpu-cores" class="form-label">CPU Cores</label>
                            <input type="number" id="cpu-cores" class="form-control" value="64" min="1">
                        </div>
                        <div class="form-group">
                            <label for="vm-count" class="form-label">Number of VMs</label>
                            <input type="number" id="vm-count" class="form-control" value="20" min="1">
                        </div>
                        <div class="form-group">
                            <label for="support-level" class="form-label">Support Level</label>
                            <select id="support-level" class="form-control">
                                <option value="basic">Basic</option>
                                <option value="standard">Standard</option>
                                <option value="premium">Premium</option>
                            </select>
                        </div>
                        <button class="btn btn--primary" onclick="calculateCosts()">Calculate Costs</button>
                    </div>
                    <div class="calculator-results" id="cost-results">
                        <h3>Cost Comparison (Annual)</h3>
                        <div class="cost-breakdown">
                            <div class="cost-item">
                                <h4>Rancher Harvester</h4>
                                <div class="cost-value" id="harvester-cost">$0 (Open Source)</div>
                                <div class="cost-details">
                                    <p>Licensing: Free</p>
                                    <p>Support: Optional</p>
                                </div>
                            </div>
                            <div class="cost-item">
                                <h4>OpenShift Virtualization</h4>
                                <div class="cost-value" id="openshift-cost">$169,280</div>
                                <div class="cost-details">
                                    <p>Licensing: $2,640/core/year</p>
                                    <p>Support: Included</p>
                                </div>
                            </div>
                            <div class="cost-item">
                                <h4>VMware vSphere</h4>
                                <div class="cost-value" id="vmware-cost">$22,400</div>
                                <div class="cost-details">
                                    <p>Licensing: $350/core/year</p>
                                    <p>Support: Included</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 VM Platform Comparison Guide. Educational purposes only.</p>
        </div>
    </footer>

    <script src="app.js"></script>
</body>
</html>