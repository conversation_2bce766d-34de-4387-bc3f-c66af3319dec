# VM Platform Comparison Guide

This project is a web-based comparison guide for three popular virtual machine (VM) platforms: Rancher Harvester, OpenShift Virtualization, and VMware vSphere. It provides a comprehensive overview of each platform, a side-by-side comparison of their features, a decision matrix tool to help users choose the best platform for their needs, a migration guide, and a cost calculator.

## Features

*   **Platform Overview:** Detailed information about each platform, including its architecture, key features, and best use cases.
*   **Side-by-Side Comparison:** An interactive table that allows users to compare the platforms based on various criteria, such as architecture, features, performance, and cost.
*   **Decision Matrix Tool:** A questionnaire that helps users determine the most suitable platform for their specific requirements by asking about their workload type, cost priority, and expected scale.
*   **Migration Guide:** Provides information on migrating from VMware to Harvester or OpenShift, as well as from a physical machine to a virtual one.
*   **Cost Calculator:** An interactive tool that estimates the annual cost of each platform based on the number of CPU cores, VMs, and desired support level. Features accurate OpenShift Virtualization bare metal licensing calculations using 2-socket/128-core increments.
*   **Responsive Design:** The application is designed to work on various screen sizes, from mobile devices to desktops.

## File Descriptions

*   `index.html`: The main HTML file that defines the structure of the web page. It includes sections for the overview, platform deep dive, side-by-side comparison, decision matrix, migration guide, and cost calculator.
*   `style.css`: The CSS file that styles the web page. It includes a modern and clean design with a light and dark theme that adapts to the user's system preferences.
*   `app.js`: The JavaScript file that adds interactivity to the web page. It handles tab navigation, the comparison table rendering, the decision matrix logic, and the cost calculation.
*   `comparison_data.json`: A JSON file that contains all the data used in the application. This includes detailed information about each platform, the comparison data, the decision matrix questions, and migration path details.

## Getting Started

To run this project, you can simply open the `index.html` file in a web browser. No special setup is required.

## Data Source

The `comparison_data.json` file is the single source of truth for all the information presented on the web page. This makes it easy to update and maintain the data. The file is divided into the following sections:

*   `platforms`: Contains detailed information about each platform, including its name, subtitle, description, architecture, features, performance, cost, pros, cons, and use cases.
*   `comparison_categories`: Defines the categories used in the side-by-side comparison table.
*   `decision_matrix`: Contains the questions and scoring logic for the decision matrix tool.
*   `migration_paths`: Provides information about the different migration paths, including the complexity, required tools, and key considerations.

## Styling

The `style.css` file provides a modern and clean design for the application. It uses CSS variables for easy customization of colors, fonts, and other visual elements. The application also supports a dark mode that automatically adapts to the user's system preferences.

## Functionality

The `app.js` file provides the following functionality:

*   **Smooth Scrolling:** Smoothly scrolls to different sections of the page when the user clicks on the navigation links.
*   **Tabbed Interface:** Allows users to switch between different platform details in the "Platform Deep Dive" section.
*   **Interactive Comparison Table:** Dynamically generates the comparison table based on the categories selected by the user.
*   **Decision Matrix:** Guides the user through a series of questions and provides a platform recommendation based on their answers.
*   **Cost Calculator:** Calculates and displays the estimated annual cost for each platform based on user input. Implements accurate OpenShift bare metal licensing model with proper scaling for 2-socket/128-core license increments.

## Recent Updates

### OpenShift Virtualization Cost Calculator Enhancement

The cost calculator has been updated to accurately reflect OpenShift Virtualization's bare metal licensing model:

*   **Licensing Structure:** Implements Red Hat's 2-socket/128-core licensing increments
*   **Accurate Scaling:** Automatically calculates the number of licenses needed based on total core count
*   **Cost Transparency:** Displays the exact number of licenses required and cores per license
*   **Real-time Updates:** Recalculates costs dynamically as users adjust input parameters
*   **Pricing Model:** Each license covers up to 128 cores at $338,400/year ($2,640 per core × 128 cores)

This update ensures users get accurate cost estimates when evaluating OpenShift Virtualization for their infrastructure needs.
