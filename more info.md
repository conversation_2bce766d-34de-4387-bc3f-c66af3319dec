<PERSON> G  
<EMAIL>  
July 09, 2025


# In-Depth OpenShift Analysis

# In-Depth Analysis: OpenShift Cost, Pricing, SKUs, and Support Levels

This comprehensive overview details the cost structure, available SKUs, support tiers, and special pricing options for Red Hat OpenShift, with a focus on OpenShift Virtualization and its relation to the broader OpenShift portfolio. It also provides context by comparing OpenShift’s pricing to Rancher Harvester and VMware vSphere.

---

## 1. OpenShift Product Portfolio and SKUs

Red Hat OpenShift offers a suite of products and SKUs tailored to different deployment models and infrastructure needs:

### **Core OpenShift SKUs**
- **Red Hat OpenShift Kubernetes Engine**: Provides core Kubernetes functionality for hybrid cloud and edge deployments.
- **Red Hat OpenShift Container Platform**: Full-featured platform for building, deploying, and running applications.
- **Red Hat OpenShift Platform Plus**: Adds advanced security, management, and automation for multi-cluster, multi-cloud environments.
- **Red Hat OpenShift Virtualization Engine**: Bare metal-only virtualization infrastructure, based on KubeVirt, for VM-centric workloads.
- **Red Hat OpenShift Data Foundation (ODF)**: Container-native storage integrated with OpenShift [[1]](https://endoflife.date/red-hat-openshift#:~:text=OpenShift%20is%20a%20family,is%20the%20OpenShift%20Container) [[2]](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20Red%20Hat%20OpenShift,which%20you%20install%20and) [[3]](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20Red%20Hat%20OpenShift,manage%20in%20a%20datacenter%2C) [[4]](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20Red%20Hat%20OpenShift,clusters%20and%20cloud%20environments.) [[5]](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20Red%20Hat%20OpenShift,hypervisor%2C%20purpose%2Dbuilt%20to%20provide) [[6]](https://futurumgroup.com/document/red-hat-openshift-data-foundation-odf-product-brief/#:~:text=Red%20Hat%20OpenShift%20Data,with%20the%20OpenShift%20Container).

### **Subscription Types**
- **Core-Pair Subscription**: Based on the aggregate number of physical cores or vCPUs across all compute nodes. Available for most OpenShift editions.
- **Bare Metal Socket-Pair Subscription**: Required for OpenShift Virtualization Engine and all self-managed OpenShift editions on bare metal. Each subscription covers up to 128 cores per socket pair and can be stacked for larger servers [[7]](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20This%20subscription%20is,3rd%20party%20hypervisor%20is).

### **Support Levels**
- Each SKU is available with either:
  - **Standard 8x5 Support**: Business hours support.
  - **Premium 24x7 Support**: Around-the-clock support for mission-critical environments [[8]](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20Available%20with%20Standard,or%20Premium%2024x7%20support).

---

## 2. OpenShift Virtualization Licensing

- **Licensing Model**: OpenShift Virtualization is licensed exclusively via the bare metal socket-pair subscription. This is designed for direct-to-hardware (bare metal) deployments and does not support third-party hypervisors.
- **Coverage**: Each socket-pair license covers up to 128 cores. For servers with more than two sockets or more than 128 cores, additional subscriptions are required.
- **Architectures Supported**: x86 and ARM [[9]](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20This%20subscription%20option,option%20for%20OpenShift%20Virtualization) [[10]](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20Can%20be%20stacked,a%20single%20subscription%20cannot).

---

## 3. Pricing Models and Cost Figures

### **Self-Managed OpenShift**
- **Subscription-Based**: Annual or multi-year subscriptions, priced per core-pair or socket-pair.
- **Core-Based Pricing**: Recent shift from CPU/socket-based to core-based licensing. This can increase costs for high-core-count servers (e.g., a 2x24-core server now requires 24 core-pair licenses instead of 2 CPU licenses) [[11]](https://www.redhat.com/en/technologies/cloud-computing/openshift/pricing#:~:text=Red%20Hat%20OpenShift%20editions%20and).

### **Cloud-Hosted OpenShift**
- **Hourly Pricing**: For managed services (e.g., OpenShift Service on AWS, Azure Red Hat OpenShift), pricing starts at ~$0.076 per hour for a 4vCPU configuration with a 3-year reserved instance.
- **SLA**: Managed services typically offer up to 99.99% uptime, depending on the provider.

### **Volume Discounts and Enterprise Agreements**
- **Volume Discounts**: Available through cloud providers and Red Hat for large-scale or long-term commitments. For example, migrating non-ROSA workloads to ROSA can yield a 75% discount on the service fee for the remaining term of the original subscription [[12]](https://www.rosaworkshop.io/rosa/14-faq/#:~:text=Yes%21%20Customers%20migrating%20non%2DROSA,term%20of%20their%20non%2DROSA).
- **Enterprise Agreements**: Major cloud providers (AWS, Azure, IBM) offer enterprise agreements with committed spend discounts (e.g., AWS Enterprise Discount Program, Azure Consumption Commitment Benefit) [[13]](https://www.redhat.com/en/technologies/cloud-computing/openshift/pricing#:~:text=Amazon%20Web%20Services%20Enterprise,Cloud%20Enterprise%20Savings%20Plan).
- **Reserved Instance Pricing**: 1-year and 3-year reserved pricing can provide 33–55% savings over on-demand rates [[14]](https://aws.amazon.com/rosa/pricing/#:~:text=control%20planes%20%28HCP%29%20clusters,55%25%20off%20the%20on%2Ddemand) [[15]](https://learn.microsoft.com/en-us/azure/cost-management-billing/reservations/prepay-red-hat-openshift#:~:text=When%20you%20prepay%20for,Red%20Hat%20OpenShift%20meters).

---

## 4. Support Tiers and SLAs

### **Support Levels**
- **Standard 8x5**: Support during business hours (8 hours/day, 5 days/week). Suitable for non-critical environments.
- **Premium 24x7**: 24/7 support for mission-critical workloads [[16]](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20SLA%3A%20A%20choice,or%20Premium%2024x7%20support).

### **Service Level Agreements (SLAs)**
- **Control Plane Availability**: Ensures core OpenShift components are operational.
- **Synthetic Availability**: Guarantees running applications and non-control-plane components are available.
- **Router, Node Group, Monitoring, and Extensions Availability**: Covers ingress, node health, monitoring, and additional Kubernetes components [[17]](https://docs.stakater.com/oaas/sla.html#:~:text=Control%20plane%20availability%20indicates,the%20main%20control%20OpenShift) [[18]](https://docs.stakater.com/oaas/sla.html#:~:text=Synthetic%20availability%20is%20the,of%20the%20cluster%27s%20Control) [[19]](https://docs.stakater.com/oaas/sla.html#:~:text=Router%20availability%20is%20the,are%20delivered%20to%20end) [[20]](https://docs.stakater.com/oaas/sla.html#:~:text=Node%20Group%20Availability%20indicates,Group%20has%20at%20least) [[21]](https://docs.stakater.com/oaas/sla.html#:~:text=Monitoring%20and%20Autoscaling%20Availability%23) [[22]](https://docs.stakater.com/oaas/sla.html#:~:text=Extensions%20availability%20means%20that,the%20runtime%20environment%20and).

### **SLA Penalties**
- **Non-Production**: Up to 10% of monthly service cost.
- **Production**: Up to 20% of monthly service cost, if SLA is not met due to provider issues [[23]](https://docs.stakater.com/oaas/sla.html#:~:text=the%20monthly%20cost%20of%20service).

---

## 5. Special Pricing and Discounts

- **Migration Discounts**: Up to 75% off for migrating to ROSA from non-ROSA OpenShift, and 50% off for IBM Cloud Pak customers moving to ROSA [[12]](https://www.rosaworkshop.io/rosa/14-faq/#:~:text=Yes%21%20Customers%20migrating%20non%2DROSA,term%20of%20their%20non%2DROSA) [[24]](https://www.rosaworkshop.io/rosa/14-faq/#:~:text=subscriptions.%20IBM%20Cloud%20Pak,rate%20over%20the%20remaining).
- **Cloud Provider Discounts**: Reserved instance and prepayment options on AWS and Azure can yield significant savings.
- **Negotiated Enterprise Pricing**: Large customers can negotiate custom pricing and discounts based on volume and commitment [[13]](https://www.redhat.com/en/technologies/cloud-computing/openshift/pricing#:~:text=Amazon%20Web%20Services%20Enterprise,Cloud%20Enterprise%20Savings%20Plan).

---

## 6. Comparative Context: Rancher Harvester and VMware vSphere

### **Rancher Harvester**
- **Pricing**: 100% open source and free. No licensing costs; only hardware and optional support from SUSE [[25]](https://www.trustradius.com/compare-products/openshift-vs-suse-harvester#:~:text=SUSE%20Harvester%20%7C%20N/A,help%20operators%20consolidate%20and).
- **Cost-Effectiveness**: Most cost-effective for organizations seeking to avoid licensing fees.

### **VMware vSphere**
- **Pricing**: Traditionally per-CPU with memory caps; recent price increases have doubled costs for some customers. Licensing is complex and can be expensive, especially for smaller deployments [[26]](https://www.peerspot.com/products/comparisons/red-hat-openshift_vs_vmware-vsphere-foundation#:~:text=Initially%2C%20licensing%20was%20per,clients%20with%20smaller%20compute) [[27]](https://www.peerspot.com/products/comparisons/red-hat-openshift_vs_vmware-vsphere-foundation#:~:text=VMware%20needs%20to%20improve,often%20seek%20more%20affordable).
- **Discounts**: Volume discounts and enterprise agreements are available but negotiated case-by-case.

### **OpenShift**
- **Pricing**: Subscription-based, with both core-pair and socket-pair options. Can be expensive for high-core-count servers, but offers robust features and support. Volume discounts and enterprise agreements can help reduce costs [[28]](https://www.peerspot.com/products/comparisons/rancher-labs_vs_red-hat-openshift#:~:text=The%20cost%20of%20OpenShift,includes%20many%20products%20and).

---

## 7. Summary Table: OpenShift Pricing and Support

| SKU/Edition                        | Licensing Model         | Support Levels         | Example Cost (Cloud) | Discounts/Agreements         |
|-------------------------------------|------------------------|-----------------------|----------------------|------------------------------|
| OpenShift Kubernetes Engine         | Core-pair subscription | Standard, Premium     | N/A                  | Volume, reserved, enterprise |
| OpenShift Container Platform        | Core-pair subscription | Standard, Premium     | N/A                  | Volume, reserved, enterprise |
| OpenShift Platform Plus             | Core-pair subscription | Standard, Premium     | N/A                  | Volume, reserved, enterprise |
| OpenShift Virtualization Engine     | Socket-pair (bare metal)| Standard, Premium    | N/A                  | Volume, reserved, enterprise |
| Managed OpenShift (ROSA, Azure, etc)| Per-hour (cloud)       | Standard, Premium     | ~$0.076/hr (4vCPU)   | Reserved, migration, volume  |

---

## 8. Key Takeaways

- **OpenShift offers multiple SKUs and support levels** to fit a wide range of deployment scenarios, from core Kubernetes to full-featured hybrid cloud platforms.
- **Licensing is primarily core-pair or socket-pair based**, with OpenShift Virtualization requiring bare metal socket-pair subscriptions.
- **Support tiers** include Standard (8x5) and Premium (24x7), with detailed SLAs and financial penalties for non-compliance.
- **Volume discounts, migration incentives, and enterprise agreements** are available, especially for large-scale or cloud-based deployments.
- **Compared to Rancher Harvester (free) and VMware vSphere (complex, costly licensing)**, OpenShift’s pricing is flexible but can be high for large, high-core-count environments. However, its robust features and support may justify the cost for many enterprises.

---

This analysis should provide a clear, data-driven understanding of OpenShift’s cost structure, SKUs, and support options, as well as how it compares to other leading VM platforms. If you need specific price quotes or a cost calculator for your environment, Red Hat and its cloud partners provide detailed pricing tools and calculators on their respective websites.


### References

1. **Red Hat OpenShift Virtualization**. [https://www.redhat.com](https://www.redhat.com/en/technologies/cloud-computing/openshift/virtualization#:~:text=OpenShift%20Virtualization%20simplifies%20the,advantage%20of%20the%20simplicity)
2. **Self-managed Red Hat OpenShift subscription guide**. [https://www.redhat.com](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20This%20subscription%20option,option%20for%20OpenShift%20Virtualization)
3. **Self-managed Red Hat OpenShift subscription guide**. [https://www.redhat.com](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20Can%20be%20stacked,a%20single%20subscription%20cannot)
4. **Self-managed Red Hat OpenShift subscription guide**. [https://www.redhat.com](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=This%20subscription%20option%20is,applicable%20to%20OpenShift%20Virtualization)
5. **veeam-kasten-and-red-hat-openshift-virtualization- ...**. [https://www.veeam.com](https://www.veeam.com/solution-briefs/veeam-kasten-and-red-hat-openshift-virtualization-reference-architecture_wp.pdf#:~:text=OpenShift%20Virtualization%20has%20several,cloud%20in%20AWS%20via)
6. **Red Hat OpenShift Service on AWS - FAQ**. [https://www.rosaworkshop.io](https://www.rosaworkshop.io/rosa/14-faq/#:~:text=Yes%21%20Customers%20migrating%20non%2DROSA,term%20of%20their%20non%2DROSA)
7. **Red Hat OpenShift Service on AWS - FAQ**. [https://www.rosaworkshop.io](https://www.rosaworkshop.io/rosa/14-faq/#:~:text=subscriptions.%20IBM%20Cloud%20Pak,rate%20over%20the%20remaining)
8. **Red Hat OpenShift pricing**. [https://www.redhat.com](https://www.redhat.com/en/technologies/cloud-computing/openshift/pricing#:~:text=Amazon%20Web%20Services%20Enterprise,Cloud%20Enterprise%20Savings%20Plan)
9. **Red Hat OpenShift Service on AWS Pricing - Amazon.com**. [https://aws.amazon.com](https://aws.amazon.com/rosa/pricing/#:~:text=control%20planes%20%28HCP%29%20clusters,55%25%20off%20the%20on%2Ddemand)
10. **Prepay for Azure Red Hat OpenShift to save costs**. [https://learn.microsoft.com](https://learn.microsoft.com/en-us/azure/cost-management-billing/reservations/prepay-red-hat-openshift#:~:text=When%20you%20prepay%20for,Red%20Hat%20OpenShift%20meters)
11. **Self-managed Red Hat OpenShift subscription guide**. [https://www.redhat.com](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20SLA%3A%20A%20choice,or%20Premium%2024x7%20support)
12. **Service Level Agreement (SLA) - OpenShift as a Service**. [https://docs.stakater.com](https://docs.stakater.com/oaas/sla.html#:~:text=Control%20plane%20availability%20indicates,the%20main%20control%20OpenShift)
13. **Service Level Agreement (SLA) - OpenShift as a Service**. [https://docs.stakater.com](https://docs.stakater.com/oaas/sla.html#:~:text=Synthetic%20availability%20is%20the,of%20the%20cluster%27s%20Control)
14. **Service Level Agreement (SLA) - OpenShift as a Service**. [https://docs.stakater.com](https://docs.stakater.com/oaas/sla.html#:~:text=Router%20availability%20is%20the,are%20delivered%20to%20end)
15. **Service Level Agreement (SLA) - OpenShift as a Service**. [https://docs.stakater.com](https://docs.stakater.com/oaas/sla.html#:~:text=Node%20Group%20Availability%20indicates,Group%20has%20at%20least)
16. **Service Level Agreement (SLA) - OpenShift as a Service**. [https://docs.stakater.com](https://docs.stakater.com/oaas/sla.html#:~:text=Monitoring%20and%20Autoscaling%20Availability%23)
17. **Service Level Agreement (SLA) - OpenShift as a Service**. [https://docs.stakater.com](https://docs.stakater.com/oaas/sla.html#:~:text=Extensions%20availability%20means%20that,the%20runtime%20environment%20and)
18. **Service Level Agreement (SLA) - OpenShift as a Service**. [https://docs.stakater.com](https://docs.stakater.com/oaas/sla.html#:~:text=the%20monthly%20cost%20of%20service)
19. **Red Hat OpenShift pricing**. [https://www.redhat.com](https://www.redhat.com/en/technologies/cloud-computing/openshift/pricing#:~:text=Red%20Hat%20OpenShift%20editions%20and)
20. **Red Hat OpenShift**. [https://endoflife.date](https://endoflife.date/red-hat-openshift#:~:text=OpenShift%20is%20a%20family,is%20the%20OpenShift%20Container)
21. **Self-managed Red Hat OpenShift subscription guide**. [https://www.redhat.com](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20Red%20Hat%20OpenShift,which%20you%20install%20and)
22. **Self-managed Red Hat OpenShift subscription guide**. [https://www.redhat.com](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20Red%20Hat%20OpenShift,manage%20in%20a%20datacenter%2C)
23. **Self-managed Red Hat OpenShift subscription guide**. [https://www.redhat.com](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20Red%20Hat%20OpenShift,clusters%20and%20cloud%20environments.)
24. **Self-managed Red Hat OpenShift subscription guide**. [https://www.redhat.com](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20Red%20Hat%20OpenShift,hypervisor%2C%20purpose%2Dbuilt%20to%20provide)
25. **Red Hat OpenShift Data Foundation (ODF) - Product Brief**. [https://futurumgroup.com](https://futurumgroup.com/document/red-hat-openshift-data-foundation-odf-product-brief/#:~:text=Red%20Hat%20OpenShift%20Data,with%20the%20OpenShift%20Container)
26. **Self-managed Red Hat OpenShift subscription guide**. [https://www.redhat.com](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20This%20subscription%20is,3rd%20party%20hypervisor%20is)
27. **Self-managed Red Hat OpenShift subscription guide**. [https://www.redhat.com](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20Available%20with%20Standard,or%20Premium%2024x7%20support)
28. **Self-managed Red Hat OpenShift subscription guide**. [https://www.redhat.com](https://www.redhat.com/en/resources/self-managed-openshift-subscription-guide#:~:text=%2D%20Red%20Hat%20OpenShift,by%20Red%20Hat%20and)
29. **Rancher Labs vs Red Hat OpenShift comparison**. [https://www.peerspot.com](https://www.peerspot.com/products/comparisons/rancher-labs_vs_red-hat-openshift#:~:text=The%20cost%20of%20OpenShift,includes%20many%20products%20and)
30. **Red Hat OpenShift vs SUSE Harvester**. [https://www.trustradius.com](https://www.trustradius.com/compare-products/openshift-vs-suse-harvester#:~:text=SUSE%20Harvester%20%7C%20N/A,help%20operators%20consolidate%20and)
31. **Red Hat OpenShift vs VMware vSphere Foundation (2025)**. [https://www.peerspot.com](https://www.peerspot.com/products/comparisons/red-hat-openshift_vs_vmware-vsphere-foundation#:~:text=Initially%2C%20licensing%20was%20per,clients%20with%20smaller%20compute)
32. **Red Hat OpenShift vs VMware vSphere Foundation (2025)**. [https://www.peerspot.com](https://www.peerspot.com/products/comparisons/red-hat-openshift_vs_vmware-vsphere-foundation#:~:text=VMware%20needs%20to%20improve,often%20seek%20more%20affordable)
