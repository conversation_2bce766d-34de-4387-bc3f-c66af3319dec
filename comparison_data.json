{"platforms": {"harvester": {"name": "Rancher Harvester", "subtitle": "Open-source HCI", "description": "Modern, open-source hyper-converged infrastructure built on Kubernetes", "architecture": {"core_technology": "KubeVirt on Kubernetes", "vm_management": "VMs as native Kubernetes objects", "container_integration": "Unified with <PERSON><PERSON>", "hypervisor": "KVM via KubeVirt", "storage": "Longhorn (distributed block storage)", "networking": "VLAN, VIP, multi-NIC support"}, "features": {"licensing": "Open-source (free)", "vm_lifecycle": "Create, clone, migrate, backup, restore", "live_migration": "Yes, with zero downtime", "high_availability": "Yes, with witness nodes", "backup_options": "NFS, S3, NAS", "gpu_support": "vGPU support (v1.3.0+)", "management_ui": "Rancher dashboard", "installation": "ISO-based, bare metal"}, "performance": {"vm_density": "Good for typical enterprise workloads", "resource_efficiency": "High (Kubernetes native)", "scalability": "Cloud-scale with Kubernetes", "tuning_requirements": "Minimal"}, "cost": {"licensing_model": "Open-source", "cost_per_core": "$0", "support_cost": "Optional commercial support", "hardware_requirements": "Commodity hardware"}, "pros": ["No licensing fees", "Deep Kubernetes integration", "Modern cloud-native architecture", "Growing ecosystem", "Unified VM/container management", "Live migration capabilities"], "cons": ["Newer platform (less mature)", "Smaller community", "Limited enterprise features vs VMware", "Requires <PERSON><PERSON><PERSON><PERSON> knowledge for advanced tasks"], "use_cases": ["Cost-sensitive organizations", "Kubernetes-native environments", "Modern infrastructure modernization", "Edge computing deployments", "Organizations avoiding vendor lock-in"]}, "openshift": {"name": "OpenShift Virtualization", "subtitle": "Hybrid cloud platform", "description": "Kubernetes-native virtualization enabling unified VM and container management", "architecture": {"core_technology": "KubeVirt on Kubernetes", "vm_management": "VMs as pods via virtualization operator", "container_integration": "Native unified platform", "hypervisor": "KVM via KubeVirt", "storage": "CSI drivers, OpenShift Data Foundation", "networking": "OpenShift SDN, OVN-Kubernetes"}, "features": {"licensing": "Subscription-based", "vm_lifecycle": "Full lifecycle via OpenShift console", "live_migration": "Yes, with Kubernetes scheduling", "high_availability": "Built-in Kubernetes HA", "backup_options": "OADP, third-party solutions", "gpu_support": "Yes, with fractional GPU support", "management_ui": "OpenShift web console", "installation": "Multiple deployment options"}, "performance": {"vm_density": "Good for hybrid workloads", "resource_efficiency": "High for mixed environments", "scalability": "Kubernetes-native scaling", "tuning_requirements": "More tuning for VM-only scenarios"}, "cost": {"licensing_model": "Subscription (per core or per node)", "cost_per_core": "$2,640-$34,848/year (depending on edition)", "support_cost": "Included in subscription", "hardware_requirements": "Standard x86 servers"}, "pros": ["Unified VM/container platform", "Enterprise-grade security", "Strong Red Hat ecosystem", "Migration tools from VMware", "CI/CD integration", "Multi-cloud support"], "cons": ["Subscription costs", "Complexity for pure VM workloads", "Requires Kubernetes expertise", "Performance overhead for some workloads"], "use_cases": ["Hybrid VM/container environments", "Infrastructure modernization", "Organizations with Red Hat ecosystem", "DevOps-focused environments", "Multi-cloud deployments"]}, "vmware": {"name": "VMware vSphere", "subtitle": "Enterprise virtualization leader", "description": "Industry-standard enterprise virtualization platform with mature ecosystem", "architecture": {"core_technology": "ESXi hypervisor", "vm_management": "Dedicated VM management via vCenter", "container_integration": "<PERSON> (add-on)", "hypervisor": "ESXi (bare metal)", "storage": "vSAN, external storage arrays", "networking": "vSphere Standard Switch, NSX"}, "features": {"licensing": "Proprietary subscription", "vm_lifecycle": "Complete enterprise lifecycle", "live_migration": "vMotion (industry-leading)", "high_availability": "vSphere HA, Fault Tolerance", "backup_options": "Extensive third-party ecosystem", "gpu_support": "vGPU, GPU passthrough", "management_ui": "vCenter Server", "installation": "ESXi on bare metal"}, "performance": {"vm_density": "Best-in-class (1.5x more VMs than OpenShift)", "resource_efficiency": "Highly optimized", "scalability": "Massive enterprise scale", "tuning_requirements": "Minimal (auto-tuning)"}, "cost": {"licensing_model": "Per-core subscription", "cost_per_core": "$50-$350/year (depending on edition)", "support_cost": "Included in subscription", "hardware_requirements": "Wide hardware compatibility"}, "pros": ["Mature, proven platform", "Best VM performance and density", "Extensive enterprise features", "Largest ecosystem", "Advanced automation (DRS, HA)", "Comprehensive support"], "cons": ["High licensing costs", "Vendor lock-in", "Complex pricing model", "Limited container support", "Broadcom acquisition concerns"], "use_cases": ["Mission-critical enterprise workloads", "High-density VM environments", "Organizations with existing VMware investments", "Legacy application support", "Maximum performance requirements"]}}, "comparison_categories": {"architecture": {"name": "Architecture & Technology", "description": "Core technology foundation and approach"}, "features": {"name": "Key Features", "description": "Primary capabilities and functionality"}, "performance": {"name": "Performance & Scalability", "description": "Performance characteristics and scaling capabilities"}, "cost": {"name": "Cost & Licensing", "description": "Pricing models and total cost of ownership"}, "migration": {"name": "Migration & Compatibility", "description": "Migration paths and compatibility considerations"}}, "decision_matrix": {"pure_vm_workloads": {"scenario": "Pure VM workloads, maximum stability", "best_choice": "vmware", "reasoning": "VMware offers the best VM density, performance, and stability for traditional virtualization"}, "hybrid_workloads": {"scenario": "Hybrid VM and container workloads", "best_choice": "openshift", "reasoning": "OpenShift provides unified management of VMs and containers with strong integration"}, "cost_optimization": {"scenario": "Cost-sensitive, open-source preference", "best_choice": "harvester", "reasoning": "Harvester offers enterprise HCI capabilities without licensing fees"}, "modernization": {"scenario": "Infrastructure modernization", "best_choice": "harvester", "reasoning": "Harvester enables modern, cloud-native infrastructure with Kubernetes integration"}, "enterprise_scale": {"scenario": "Large enterprise, mission-critical", "best_choice": "vmware", "reasoning": "VMware provides the most mature platform with extensive enterprise features"}}, "migration_paths": {"vmware_to_harvester": {"complexity": "Medium", "tools": ["vm-import-controller", "qemu-img conversion", "Co<PERSON><PERSON>"], "considerations": "Manual process, requires disk conversion, network mapping"}, "vmware_to_openshift": {"complexity": "Low-Medium", "tools": ["Migration Toolkit for Virtualization (MTV)", "Red Hat migration tools"], "considerations": "Automated migration tools available, good support"}, "physical_to_any": {"complexity": "High", "tools": ["Various P2V tools", "Cloning solutions"], "considerations": "Requires physical-to-virtual conversion first"}}}