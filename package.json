{"name": "vm-platform-comparison", "version": "1.0.0", "description": "A web-based comparison guide for three popular virtual machine (VM) platforms: Rancher Harvester, OpenShift Virtualization, and VMware vSphere", "main": "index.html", "scripts": {"dev": "python3 -m http.server 8000", "start": "python3 -m http.server 8000", "serve": "python3 -m http.server 8000"}, "keywords": ["vm", "virtualization", "comparison", "rancher", "harvester", "openshift", "vmware", "vsphere"], "author": "", "license": "MIT", "devDependencies": {}, "repository": {"type": "git", "url": "."}}