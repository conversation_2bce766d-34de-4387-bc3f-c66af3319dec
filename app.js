// Platform data
const platformData = {
  "harvester": {
    "name": "Rancher Harvester",
    "subtitle": "Open-source HCI",
    "description": "Modern, open-source hyper-converged infrastructure built on Kubernetes",
    "architecture": {
      "core_technology": "KubeVirt on Kubernetes",
      "vm_management": "VMs as native Kubernetes objects",
      "container_integration": "Unified with Rancher",
      "hypervisor": "KVM via KubeVirt",
      "storage": "Longhorn (distributed block storage)",
      "networking": "VLAN, VIP, multi-NIC support"
    },
    "features": {
      "licensing": "Open-source (free)",
      "vm_lifecycle": "Create, clone, migrate, backup, restore",
      "live_migration": "Yes, with zero downtime",
      "high_availability": "Yes, with witness nodes",
      "backup_options": "NFS, S3, NAS",
      "gpu_support": "vGPU support (v1.3.0+)",
      "management_ui": "Rancher dashboard",
      "installation": "ISO-based, bare metal"
    },
    "performance": {
      "vm_density": "Good for typical enterprise workloads",
      "resource_efficiency": "High (Kubernetes native)",
      "scalability": "Cloud-scale with Kubernetes",
      "tuning_requirements": "Minimal"
    },
    "cost": {
      "licensing_model": "Open-source",
      "cost_per_core": "$0",
      "support_cost": "Optional commercial support",
      "hardware_requirements": "Commodity hardware"
    }
  },
  "openshift": {
    "name": "OpenShift Virtualization",
    "subtitle": "Hybrid cloud platform",
    "description": "Kubernetes-native virtualization enabling unified VM and container management",
    "architecture": {
      "core_technology": "KubeVirt on Kubernetes",
      "vm_management": "VMs as pods via virtualization operator",
      "container_integration": "Native unified platform",
      "hypervisor": "KVM via KubeVirt",
      "storage": "CSI drivers, OpenShift Data Foundation",
      "networking": "OpenShift SDN, OVN-Kubernetes"
    },
    "features": {
      "licensing": "Subscription-based",
      "vm_lifecycle": "Full lifecycle via OpenShift console",
      "live_migration": "Yes, with Kubernetes scheduling",
      "high_availability": "Built-in Kubernetes HA",
      "backup_options": "OADP, third-party solutions",
      "gpu_support": "Yes, with fractional GPU support",
      "management_ui": "OpenShift web console",
      "installation": "Multiple deployment options"
    },
    "performance": {
      "vm_density": "Good for hybrid workloads",
      "resource_efficiency": "High for mixed environments",
      "scalability": "Kubernetes-native scaling",
      "tuning_requirements": "More tuning for VM-only scenarios"
    },
    "cost": {
      "licensing_model": "Subscription (per core or per node)",
      "cost_per_core": "$2,640/year",
      "support_cost": "Included in subscription",
      "hardware_requirements": "Standard x86 servers"
    }
  },
  "vmware": {
    "name": "VMware vSphere",
    "subtitle": "Enterprise virtualization leader",
    "description": "Industry-standard enterprise virtualization platform with mature ecosystem",
    "architecture": {
      "core_technology": "ESXi hypervisor",
      "vm_management": "Dedicated VM management via vCenter",
      "container_integration": "Via Tanzu (add-on)",
      "hypervisor": "ESXi (bare metal)",
      "storage": "vSAN, external storage arrays",
      "networking": "vSphere Standard Switch, NSX"
    },
    "features": {
      "licensing": "Proprietary subscription",
      "vm_lifecycle": "Complete enterprise lifecycle",
      "live_migration": "vMotion (industry-leading)",
      "high_availability": "vSphere HA, Fault Tolerance",
      "backup_options": "Extensive third-party ecosystem",
      "gpu_support": "vGPU, GPU passthrough",
      "management_ui": "vCenter Server",
      "installation": "ESXi on bare metal"
    },
    "performance": {
      "vm_density": "Best-in-class (1.5x more VMs than OpenShift)",
      "resource_efficiency": "Highly optimized",
      "scalability": "Massive enterprise scale",
      "tuning_requirements": "Minimal (auto-tuning)"
    },
    "cost": {
      "licensing_model": "Per-core subscription",
      "cost_per_core": "$350/year",
      "support_cost": "Included in subscription",
      "hardware_requirements": "Wide hardware compatibility"
    }
  }
};

// Comparison data structure
const comparisonData = {
  architecture: [
    {
      feature: "Core Technology",
      harvester: "KubeVirt on Kubernetes",
      openshift: "KubeVirt on Kubernetes",
      vmware: "ESXi hypervisor"
    },
    {
      feature: "VM Management",
      harvester: "VMs as native Kubernetes objects",
      openshift: "VMs as pods via virtualization operator",
      vmware: "Dedicated VM management via vCenter"
    },
    {
      feature: "Container Integration",
      harvester: "Unified with Rancher",
      openshift: "Native unified platform",
      vmware: "Via Tanzu (add-on)"
    },
    {
      feature: "Storage",
      harvester: "Longhorn (distributed block storage)",
      openshift: "CSI drivers, OpenShift Data Foundation",
      vmware: "vSAN, external storage arrays"
    }
  ],
  features: [
    {
      feature: "Licensing",
      harvester: "Open-source (free)",
      openshift: "Subscription-based",
      vmware: "Proprietary subscription"
    },
    {
      feature: "Live Migration",
      harvester: "Yes, with zero downtime",
      openshift: "Yes, with Kubernetes scheduling",
      vmware: "vMotion (industry-leading)"
    },
    {
      feature: "High Availability",
      harvester: "Yes, with witness nodes",
      openshift: "Built-in Kubernetes HA",
      vmware: "vSphere HA, Fault Tolerance"
    },
    {
      feature: "GPU Support",
      harvester: "vGPU support (v1.3.0+)",
      openshift: "Yes, with fractional GPU support",
      vmware: "vGPU, GPU passthrough"
    }
  ],
  performance: [
    {
      feature: "VM Density",
      harvester: "Good for typical enterprise workloads",
      openshift: "Good for hybrid workloads",
      vmware: "Best-in-class (1.5x more VMs)"
    },
    {
      feature: "Resource Efficiency",
      harvester: "High (Kubernetes native)",
      openshift: "High for mixed environments",
      vmware: "Highly optimized"
    },
    {
      feature: "Scalability",
      harvester: "Cloud-scale with Kubernetes",
      openshift: "Kubernetes-native scaling",
      vmware: "Massive enterprise scale"
    },
    {
      feature: "Tuning Requirements",
      harvester: "Minimal",
      openshift: "More tuning for VM-only scenarios",
      vmware: "Minimal (auto-tuning)"
    }
  ],
  cost: [
    {
      feature: "Licensing Model",
      harvester: "Open-source",
      openshift: "Subscription (per core)",
      vmware: "Per-core subscription"
    },
    {
      feature: "Cost per Core/Year",
      harvester: "$0",
      openshift: "$2,640",
      vmware: "$350"
    },
    {
      feature: "Support Cost",
      harvester: "Optional commercial support",
      openshift: "Included in subscription",
      vmware: "Included in subscription"
    },
    {
      feature: "Hardware Requirements",
      harvester: "Commodity hardware",
      openshift: "Standard x86 servers",
      vmware: "Wide hardware compatibility"
    }
  ]
};

// Decision matrix logic
const decisionMatrix = {
  questions: [
    {
      id: "workload-type",
      question: "What type of workloads will you primarily run?",
      options: [
        { value: "pure-vm", text: "Pure VM workloads", weight: { vmware: 3, openshift: 1, harvester: 2 } },
        { value: "hybrid", text: "Hybrid VM + Container workloads", weight: { vmware: 1, openshift: 3, harvester: 2 } },
        { value: "container-focus", text: "Container-focused with some VMs", weight: { vmware: 0, openshift: 3, harvester: 2 } }
      ]
    },
    {
      id: "cost-priority",
      question: "How important is cost optimization?",
      options: [
        { value: "cost-critical", text: "Cost is critical", weight: { vmware: 0, openshift: 1, harvester: 3 } },
        { value: "cost-important", text: "Cost is important", weight: { vmware: 1, openshift: 2, harvester: 3 } },
        { value: "cost-secondary", text: "Performance over cost", weight: { vmware: 3, openshift: 2, harvester: 1 } }
      ]
    },
    {
      id: "scale",
      question: "What is your expected scale?",
      options: [
        { value: "small", text: "Small (< 100 VMs)", weight: { vmware: 1, openshift: 2, harvester: 3 } },
        { value: "medium", text: "Medium (100-1000 VMs)", weight: { vmware: 2, openshift: 2, harvester: 2 } },
        { value: "large", text: "Large (1000+ VMs)", weight: { vmware: 3, openshift: 2, harvester: 1 } }
      ]
    }
  ]
};

// State management
let decisionState = {
  currentQuestion: 0,
  answers: {},
  scores: { vmware: 0, openshift: 0, harvester: 0 }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
  initializeApp();
});

function initializeApp() {
  setupNavigation();
  setupTabs();
  setupComparison();
  setupDecisionTool();
  setupPlatformCards();
  calculateCosts(); // Initial cost calculation
}

// Navigation setup
function setupNavigation() {
  const navLinks = document.querySelectorAll('.nav__link');
  navLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const targetId = this.getAttribute('href').substring(1);
      const targetElement = document.getElementById(targetId);
      if (targetElement) {
        targetElement.scrollIntoView({ behavior: 'smooth' });
      }
    });
  });
}

// Tab functionality
function setupTabs() {
  const tabButtons = document.querySelectorAll('.tab-button');
  const tabPanes = document.querySelectorAll('.tab-pane');

  tabButtons.forEach(button => {
    button.addEventListener('click', function() {
      const targetTab = this.dataset.tab;
      
      // Remove active class from all buttons and panes
      tabButtons.forEach(btn => btn.classList.remove('active'));
      tabPanes.forEach(pane => pane.classList.remove('active'));
      
      // Add active class to clicked button and corresponding pane
      this.classList.add('active');
      document.getElementById(`tab-${targetTab}`).classList.add('active');
    });
  });
}

// Platform cards interaction
function setupPlatformCards() {
  const platformCards = document.querySelectorAll('.platform-card');
  platformCards.forEach(card => {
    card.addEventListener('click', function() {
      const platform = this.dataset.platform;
      const platformsSection = document.getElementById('platforms');
      const targetTab = document.querySelector(`[data-tab="${platform}"]`);
      
      if (targetTab) {
        // Scroll to platforms section
        platformsSection.scrollIntoView({ behavior: 'smooth' });
        
        // Activate the corresponding tab after a short delay
        setTimeout(() => {
          targetTab.click();
        }, 500);
      }
    });
  });
}

// Comparison table setup
function setupComparison() {
  const toggleButtons = document.querySelectorAll('.toggle-btn');
  const comparisonBody = document.getElementById('comparison-body');
  
  toggleButtons.forEach(button => {
    button.addEventListener('click', function() {
      this.classList.toggle('active');
      renderComparisonTable();
    });
  });
  
  // Initial render
  renderComparisonTable();
}

function renderComparisonTable() {
  const comparisonBody = document.getElementById('comparison-body');
  const activeCategories = Array.from(document.querySelectorAll('.toggle-btn.active'))
    .map(btn => btn.dataset.category);
  
  let html = '';
  
  activeCategories.forEach(category => {
    if (comparisonData[category]) {
      html += `<div class="comparison-category">${getCategoryName(category)}</div>`;
      
      comparisonData[category].forEach(row => {
        html += `
          <div class="comparison-row">
            <div class="comparison-cell"><strong>${row.feature}</strong></div>
            <div class="comparison-cell">${row.harvester}</div>
            <div class="comparison-cell">${row.openshift}</div>
            <div class="comparison-cell">${row.vmware}</div>
          </div>
        `;
      });
    }
  });
  
  comparisonBody.innerHTML = html;
}

function getCategoryName(category) {
  const categoryNames = {
    architecture: 'Architecture & Technology',
    features: 'Key Features',
    performance: 'Performance & Scalability',
    cost: 'Cost & Licensing'
  };
  return categoryNames[category] || category;
}

// Decision tool setup
function setupDecisionTool() {
  const questions = document.querySelectorAll('.question');
  const optionButtons = document.querySelectorAll('.option-btn');
  
  optionButtons.forEach(button => {
    button.addEventListener('click', function() {
      const questionElement = this.closest('.question');
      const questionId = questionElement.dataset.question;
      const value = this.dataset.value;
      
      // Remove selected class from all options in this question
      questionElement.querySelectorAll('.option-btn').forEach(btn => 
        btn.classList.remove('selected')
      );
      
      // Add selected class to clicked option
      this.classList.add('selected');
      
      // Store answer
      decisionState.answers[questionId] = value;
      
      // Move to next question or show results
      setTimeout(() => {
        nextQuestion();
      }, 300);
    });
  });
  
  // Show first question
  showQuestion(0);
}

function showQuestion(index) {
  const questions = document.querySelectorAll('.question');
  questions.forEach((q, i) => {
    q.classList.toggle('active', i === index);
  });
  decisionState.currentQuestion = index;
}

function nextQuestion() {
  const totalQuestions = decisionMatrix.questions.length;
  
  if (decisionState.currentQuestion < totalQuestions - 1) {
    showQuestion(decisionState.currentQuestion + 1);
  } else {
    calculateRecommendation();
  }
}

function calculateRecommendation() {
  // Reset scores
  decisionState.scores = { vmware: 0, openshift: 0, harvester: 0 };
  
  // Calculate scores based on answers
  Object.keys(decisionState.answers).forEach(questionId => {
    const answer = decisionState.answers[questionId];
    const question = decisionMatrix.questions.find(q => q.id === questionId);
    
    if (question) {
      const option = question.options.find(opt => opt.value === answer);
      if (option) {
        Object.keys(option.weight).forEach(platform => {
          decisionState.scores[platform] += option.weight[platform];
        });
      }
    }
  });
  
  // Find the platform with highest score
  const recommendedPlatform = Object.keys(decisionState.scores).reduce((a, b) => 
    decisionState.scores[a] > decisionState.scores[b] ? a : b
  );
  
  showRecommendation(recommendedPlatform);
}

function showRecommendation(platform) {
  const resultDiv = document.getElementById('decision-result');
  const platformInfo = platformData[platform];
  
  const html = `
    <div class="recommendation">
      <h4>Recommended Platform: ${platformInfo.name}</h4>
      <p><strong>${platformInfo.subtitle}</strong></p>
      <p>${platformInfo.description}</p>
      <div style="margin-top: 16px;">
        <strong>Key Benefits for Your Needs:</strong>
        <ul style="text-align: left; margin-top: 8px;">
          ${getRecommendationReasons(platform)}
        </ul>
      </div>
    </div>
    <button class="btn btn--secondary" onclick="resetDecisionTool()" style="margin-top: 16px;">
      Start Over
    </button>
  `;
  
  resultDiv.innerHTML = html;
}

function getRecommendationReasons(platform) {
  const reasons = {
    harvester: [
      'No licensing costs - completely open source',
      'Modern Kubernetes-native architecture',
      'Unified VM and container management',
      'Perfect for cost-conscious organizations'
    ],
    openshift: [
      'Seamless hybrid VM and container workloads',
      'Enterprise-grade security and compliance',
      'Strong Red Hat ecosystem integration',
      'Excellent for modernization initiatives'
    ],
    vmware: [
      'Best-in-class VM performance and density',
      'Mature enterprise features and stability',
      'Extensive third-party ecosystem',
      'Proven track record for mission-critical workloads'
    ]
  };
  
  return reasons[platform].map(reason => `<li>${reason}</li>`).join('');
}

function resetDecisionTool() {
  decisionState = {
    currentQuestion: 0,
    answers: {},
    scores: { vmware: 0, openshift: 0, harvester: 0 }
  };
  
  // Reset UI
  document.querySelectorAll('.option-btn').forEach(btn => 
    btn.classList.remove('selected')
  );
  
  showQuestion(0);
  
  const resultDiv = document.getElementById('decision-result');
  resultDiv.innerHTML = `
    <h3>Get Your Recommendation</h3>
    <p>Answer the questions above to get a personalized platform recommendation.</p>
  `;
}

// Cost calculator
function calculateCosts() {
  const cpuCores = parseInt(document.getElementById('cpu-cores').value) || 64;
  const vmCount = parseInt(document.getElementById('vm-count').value) || 20;
  const supportLevel = document.getElementById('support-level').value || 'standard';
  
  // Cost calculations
  const costs = {
    harvester: {
      licensing: 0,
      support: supportLevel === 'premium' ? 15000 : supportLevel === 'standard' ? 8000 : 0,
      total: 0
    },
    openshift: {
      licensing: cpuCores * 2640, // $2,640 per core per year
      support: 0, // Included
      total: 0
    },
    vmware: {
      licensing: cpuCores * 350, // $350 per core per year
      support: 0, // Included
      total: 0
    }
  };
  
  // Calculate totals
  Object.keys(costs).forEach(platform => {
    costs[platform].total = costs[platform].licensing + costs[platform].support;
  });
  
  // Update UI
  document.getElementById('harvester-cost').textContent = 
    costs.harvester.total === 0 ? '$0 (Open Source)' : `$${costs.harvester.total.toLocaleString()}`;
  
  document.getElementById('openshift-cost').textContent = 
    `$${costs.openshift.total.toLocaleString()}`;
  
  document.getElementById('vmware-cost').textContent = 
    `$${costs.vmware.total.toLocaleString()}`;
  
  // Update details
  updateCostDetails(costs);
}

function updateCostDetails(costs) {
  const details = {
    harvester: `
      <p>Licensing: Free</p>
      <p>Support: ${costs.harvester.support > 0 ? `$${costs.harvester.support.toLocaleString()}` : 'Optional'}</p>
    `,
    openshift: `
      <p>Licensing: $${costs.openshift.licensing.toLocaleString()}</p>
      <p>Support: Included</p>
    `,
    vmware: `
      <p>Licensing: $${costs.vmware.licensing.toLocaleString()}</p>
      <p>Support: Included</p>
    `
  };
  
  // Update cost details in the DOM
  const costItems = document.querySelectorAll('.cost-item');
  costItems.forEach((item, index) => {
    const platform = ['harvester', 'openshift', 'vmware'][index];
    const detailsDiv = item.querySelector('.cost-details');
    if (detailsDiv) {
      detailsDiv.innerHTML = details[platform];
    }
  });
}

// Event listeners for cost calculator inputs
document.addEventListener('DOMContentLoaded', function() {
  const cpuInput = document.getElementById('cpu-cores');
  const vmInput = document.getElementById('vm-count');
  const supportSelect = document.getElementById('support-level');
  
  if (cpuInput) cpuInput.addEventListener('input', calculateCosts);
  if (vmInput) vmInput.addEventListener('input', calculateCosts);
  if (supportSelect) supportSelect.addEventListener('change', calculateCosts);
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  });
});

// Add loading states and animations
function addLoadingState(element) {
  element.classList.add('loading');
}

function removeLoadingState(element) {
  element.classList.remove('loading');
}

function addFadeInAnimation(element) {
  element.classList.add('fade-in');
  setTimeout(() => {
    element.classList.remove('fade-in');
  }, 300);
}

// Utility functions
function formatCurrency(amount) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
}

function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Add intersection observer for animations
const observerOptions = {
  threshold: 0.1,
  rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      entry.target.classList.add('fade-in');
    }
  });
}, observerOptions);

// Observe elements that should animate on scroll
document.addEventListener('DOMContentLoaded', function() {
  const elementsToObserve = document.querySelectorAll('.platform-card, .tab-pane, .comparison-table, .migration-card');
  elementsToObserve.forEach(el => {
    observer.observe(el);
  });
});

// Export functions for global access
window.calculateCosts = calculateCosts;
window.resetDecisionTool = resetDecisionTool;